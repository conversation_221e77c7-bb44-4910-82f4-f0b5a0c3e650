# Taro 小程序融合版

小程序脚手架-taro 版

_注意：_

> 全局变量使用全大写 + 下划线风格
> 接口的命名，统一还是 I 开头区分
> 代码提交规范间 .commitlintrc.js

### iconfont

[iconfont 使用参照](https://www.iconfont.cn/manage/index?spm=a313x.7781069.1998910419.12&manage_type=myprojects&projectId=2239967)

1. 更新图标库

```bash
  npx iconfont-taro
```

### 项目结构

项目采用融合项目方案，脚手架项目的 project 目录下存放医院项目(如 p099)

### 命令相关

1. 安装依赖

```bash
  yarn
```

2. 编译命令(以微信为例)
   _监听编译开发环境_

```bash
  npm run dev:weapp --dir=p099
```

_监听编译测试环境_

```bash
  npm run dev:weapp --dir=p099 --env=uat
```

_监听编译测试环境2_

```bash
  npm run dev:weapp --dir=p099 --env=uuat
```

_监听编译线上环境_

```bash
  npm run dev:weapp --dir=p099 --env=prod
```

_压缩编译开发环境_

```bash
  npm run uat:weapp --dir=p099 --env=dev
```

_压缩编译测试环境_

```bash
  npm run uat:weapp --dir=p099
```

_压缩编译测试环境2_

```bash
  npm run uuat:weapp --dir=p099
```

_压缩编译线上环境_
```bash
  npm run build:weapp --dir=p099
```

_开发环境上传图片至服务器_

```bash
  npm run ftp --dir=p099
```
