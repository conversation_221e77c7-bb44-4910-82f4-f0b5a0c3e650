// babel-preset-taro 更多选项和默认值：
// https://github.com/NervJS/taro/blob/next/packages/babel-preset-taro/README.md
var camel2Dash = require('camel-2-dash');
const path = require('path');

module.exports = {
  plugins: [
    'lodash',
    [
      "import",
      {
        "libraryName": "taro-ui",
        "libraryDirectory": "lib/components",
        "customName": (name) => {
          var newName = camel2Dash(name.replace(/^at-/, ''));
          // modal-header
          // modal-content
          // modal-action
          // action-sheet-item
          // list-item
          if (newName === 'modal-header') {
            return 'taro-ui/lib/components/modal/header';
          } else if (newName === 'modal-content') {
            return 'taro-ui/lib/components/modal/content';
          } else if (newName === 'modal-action') {
            return 'taro-ui/lib/components/modal/action';
          } else if (newName === 'action-sheet-item') {
            return 'taro-ui/lib/components/action-sheet/body/item';
          } else if (newName === 'list-item') {
            return 'taro-ui/lib/components/list/item';
          } else {
            return 'taro-ui/lib/components/' + newName;
          }
        }
      },
      "taro-ui"
    ],
    [ // 自动 import 组件插件
      path.join(__dirname, './plugins/babel-component-auto-import-plugin.js'),
      {
        // name: 组件名称, path: 组件地址
        components: [
          {
            name: 'NtmAd',
            path: '@com/ad/index',
          },
        ],
        // components: [],
      },
    ],
  ],
  presets: [
    ['taro', {
      framework: 'react',
      ts: true
    }],
  ]
}
