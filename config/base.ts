import path from 'path';
import LodashModuleReplacementPlugin from 'lodash-webpack-plugin';

import { ES6_PKG_LIST } from './utils/consts';
import { getPlugins, getCopyRule, getModuleRule } from './utils/config';

const projectDirName = globalThis.projectDirName;
const plugins = getPlugins(projectDirName);
const copyRule = getCopyRule(projectDirName);
const moduleRule = getModuleRule(projectDirName);

const baseConfig = {
  projectName: '智慧医院',
  date: '2021-01-15',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2
  },
  sourceRoot: `project/${projectDirName}/src`,
  outputRoot: `dist/${projectDirName}/${process.env.TARO_ENV}`,
  plugins,
  alias: {
    '@com': path.resolve(__dirname, '..', `project/${projectDirName}/src/components`),
    '@config': path.resolve(__dirname, '..', `project/${projectDirName}/src/config`),
    '@res': path.resolve(__dirname, '..', `project/${projectDirName}/src/resources`),
    '@utils': path.resolve(__dirname, '..', `project/${projectDirName}/src/utils`),
    '@@': path.resolve(__dirname, '..', `project/${projectDirName}/src`),
    '@': path.resolve(__dirname, '..', `project/${projectDirName}/src`),
  },
  defineConstants: {
    PRIMARY_COLOR: '"#3ECEB6"',
    HIS_NAME: '"海鹚虚拟医院"',
    NPM_SCRIPT_EVENT: JSON.stringify(process.env.npm_lifecycle_event),
    KEN_IS_PRODUCTION_BRANCH: JSON.stringify(process.env.KEN_IS_PRODUCTION_BRANCH), // 是否是生产分支
    KEN_IS_PRODUCTION_COMMAND: JSON.stringify(process.env.KEN_IS_PRODUCTION_COMMAND), // 是否是生产打包命令
  },
  copy: {
    patterns: copyRule,
    options: {}
  },
  framework: 'react',
  sass: {
    resource: path.resolve(__dirname, `../project/${projectDirName}/src/resources/styles/mixins.scss`),
  },
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {

        }
      },
      url: {
        enable: true,
        config: {
          limit: 1024 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'global', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    compile: {
      exclude: [
        path.resolve(__dirname, `../project/${projectDirName}/src/pkg2/wemark/remarkable.js`),
      ],
      include: [
        modulePath => (ES6_PKG_LIST.some(pkgName => (modulePath.includes(pkgName))))
      ]
    },
    webpackChain: (chain) => {
      const nomorlCss = chain.module.rules.store.get('nomorlCss');
      nomorlCss.oneOfs.store.delete('0');
      chain.plugin()
        .use(LodashModuleReplacementPlugin)
        .end();

      chain.merge({
        module: {
          rule: moduleRule
        }
      })
    }
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
        }
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
  },
}

export default baseConfig;
