import path from 'path';

const cdnprefix = `https://sstatic.med.gzhc365.com/${globalThis.cdnDir}`;

export default {
  env: {
    NODE_ENV: '"development"'
  },
  defineConstants: {
    ENV: '"dev"', // 环境变量
    API_PREFIX: '"https://smix.med.gzhc365.com"',
    CDN_PREFIX: JSON.stringify(cdnprefix),
    PAY_PREFIX: '"https://spay.med.gzhc365.com"',
    IM_PREFIX: '"https://sim.med.gzhc365.com"',
    ENV_PREFIX: JSON.stringify(globalThis.runEnv), // 保留(兼容历史)
  },
  sass: {
    data: `$CDN_PREFIX: ${JSON.stringify(cdnprefix)};`
  },
  plugins: [
    path.resolve(__dirname, '../plugins/static-upload/index.ts')
  ],
  mini: {},
  h5: {}
};
