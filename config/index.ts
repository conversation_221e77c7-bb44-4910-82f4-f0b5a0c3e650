import chalk from 'chalk';
import fs from 'fs';
import path from 'path';
import { isPage } from './utils/utils';
import { COMPATIBLE_ENV_MAP } from './utils/consts';

chalk.enabled = true;
chalk.level = 1;

// 全局变量
globalThis.newRunEnv = process.env.npm_config_env || process.env.RUN_ENV;
globalThis.runEnv = COMPATIBLE_ENV_MAP[globalThis.newRunEnv]; // 兼容历史的环境变量
globalThis.projectDirName = process.env.npm_config_dir || ''; // 子工程名
globalThis.cdnDir = `miniprogram-static/fe-his-mixapp/${globalThis.projectDirName}`;
globalThis.isPage = isPage;

if (!globalThis.projectDirName) {
  console.log(chalk.red('请指定工程名，如：npm run start --dir=p099'));
  process.exit(-1);
}

if (!fs.existsSync(`project/${globalThis.projectDirName}`)) {
  console.log(chalk.red(`指定工程名 ${globalThis.projectDirName} 不存在！`));
  process.exit(-1);
}

// 脚手架默认环境配置路径
const runEnvConfigPath = path.resolve(__dirname, `./${globalThis.newRunEnv}.ts`);
if (!fs.existsSync(runEnvConfigPath)) {
  console.log(`请在脚手架工程添加 ${globalThis.newRunEnv} 环境的配置文件`);
  process.exit(-1);
}

// 子工程配置
const childConfigPath = path.resolve(__dirname, `../project/${globalThis.projectDirName}/config/index.ts`);  // 子工程配置路径
let childConfig = {};
if (fs.existsSync(childConfigPath)) {
  childConfig = require(childConfigPath);
}

export default function (merge) {
  // 动态加载可直接使用globalThis中的全局变量
  const baseConfig = require('./base').default;
  const runEnvConfig = require(runEnvConfigPath).default;
  const merConfig = merge(baseConfig, runEnvConfig, childConfig);
  return merConfig;
}
