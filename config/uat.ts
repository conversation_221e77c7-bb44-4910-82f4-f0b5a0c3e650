const cdnprefix = `https://ustatic.med.gzhc365.com/${globalThis.cdnDir}`;

export default {
  env: {
    NODE_ENV: '"production"'
  },
  defineConstants: {
    ENV: '"uat"', // 环境变量
    API_PREFIX: '"https://umix.med.gzhc365.com"',
    CDN_PREFIX: JSON.stringify(cdnprefix),
    PAY_PREFIX: '"https://upay.med.gzhc365.com"',
    IM_PREFIX: '"https://uim.med.gzhc365.com"',
    ENV_PREFIX: JSON.stringify(globalThis.runEnv), // 保留(兼容历史)
  },
  sass: {
    data: `$CDN_PREFIX: ${JSON.stringify(cdnprefix)};`
  },
  mini: {
    optimizeMainPackage: {
      // alipay打开有问题，暂仅小程序默认打开
      enable: process.env.TARO_ENV === 'weapp',
    },
  },
  h5: {}
};
