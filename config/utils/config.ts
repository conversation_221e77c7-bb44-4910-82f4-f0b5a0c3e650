import path from 'path';
import fs from 'fs';
import chalk from 'chalk';

import { CHANNEL_PROJECT_CONFIG, MJS_PKG_LIST } from './consts';
import { isPage } from './utils';

export const getPlugins = (projectDirName) => {
  const plugins: any[] = [path.resolve(__dirname, '../../plugins/api-merge/index.ts')]
  return plugins;
}

export const getCopyRule = (projectDirName) => {
  const copyRule: any[] = [];
  const fileSuffix = CHANNEL_PROJECT_CONFIG[process.env.TARO_ENV].configFileSuffix || 'config';
  const projectConfPath = path.resolve(__dirname, `../../project/${projectDirName}/project.${fileSuffix}.json`);
  const garyTabBarImgPath = path.resolve(__dirname, `../../project/${projectDirName}/src/resources/images/tabbargray/`);
  const wemarkPath = path.resolve(__dirname, `../../project/${projectDirName}/src/pkg2/wemark/`);

  if (fs.existsSync(wemarkPath)) {
    copyRule.push({
      from: wemarkPath,
      to: path.resolve(__dirname, `../../dist/${projectDirName}/${process.env.TARO_ENV}/pkg2/wemark/`)
    });
  }
  if (fs.existsSync(garyTabBarImgPath)) {
    copyRule.push({
      from: garyTabBarImgPath,
      to: path.resolve(__dirname, `../../dist/${projectDirName}/${process.env.TARO_ENV}/resources/images/tabbargray/`)
    });
  }

  if (fs.existsSync(projectConfPath)) {
    copyRule.push({
      from: projectConfPath,
      to: path.resolve(__dirname, `../../dist/${projectDirName}/${process.env.TARO_ENV}/project.${fileSuffix}.json`)
    });
  }

  if (process.env.TARO_ENV === 'alipay') {
    // 支付宝2.0构建文件
    const alipayMiniJson = path.resolve(__dirname, `../../project/${projectDirName}/mini.project.json`);
    if (fs.existsSync(alipayMiniJson)) {
      copyRule.push({
        from: alipayMiniJson,
        to: path.resolve(__dirname, `../../dist/${projectDirName}/${process.env.TARO_ENV}/mini.project.json`)
      });
    } else {
      copyRule.push({
        from: path.resolve(__dirname, '../../mini.project.json'),
        to: path.resolve(__dirname, `../../dist/${projectDirName}/${process.env.TARO_ENV}/mini.project.json`)
      });
    }
  }

  return copyRule;
}

export const getModuleRule = (projectDirName) => {
  const rule: any = {
    mjsLoader: {
      test: /\.mjs$/,
      include: [modulePath => MJS_PKG_LIST.some(pkgName => modulePath.includes(pkgName))],
      use: [
        {
          loader: 'babel-loader',
        },
      ],
    },
  }

  // 容器注入
  const rootFilePath = path.resolve(__dirname, `../../project/${projectDirName}/src/root/index.tsx`);
  // 组件注入
  const appendFilePath = path.resolve(__dirname, `../../project/${projectDirName}/src/root/append.tsx`);

  if (fs.existsSync(rootFilePath)) {
    rule.injectRootComponentLoader = {
      test: /\.(tsx|jsx)$/,
      use: [
        {
          loader: '@haici/taro-inject-loader',
          options: {
            importPath: '@/root/index',
            injectType: 'wrap',  // 容器
            isPage,
          },
        },
      ],
    }
  } else if (fs.existsSync(appendFilePath)) {
    rule.injectRootComponentLoader = {
      test: /\.(tsx|jsx)$/,
      use: [
        {
          loader: '@haici/taro-inject-loader',
          options: {
            importPath: '@/root/append',
            injectType: 'append', // 插入
            isPage,
          },
        },
      ],
    }
  } else {
    const errText = chalk.red(`错误：项目 ${chalk.yellow(projectDirName)} 中不存在 /src/root/index.tsx 或 /src/root/append.tsx`);
    const tipText = chalk.blueBright('请参考文档添加：https://haici.yuque.com/zpgq9c/lgoow2/smqurp');
    console.log(errText);
    console.log(tipText);
    process.exit(-1);
  }
  return rule;
}
