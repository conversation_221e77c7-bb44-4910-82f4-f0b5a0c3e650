// 兼容历史环境变量
export const COMPATIBLE_ENV_MAP = {
  dev: 's',
  uat: 'u',
  uuat: 'uu',
  prod: ''
}

/**
 * 以下第三方依赖存在es6+代码，需要添加到babel-loader编译成es5,
 * 否则微信开发者工具会提示转es5才能上传，导致上传包体积变大。
 */
export const ES6_PKG_LIST = ['query-string', 'split-on-first'];

/**
 * 以下第三方依赖加载的是.mjs文件，同时存在存在es6+代码，需要添加到babel-loader编译成es5,
 * 由于taro当前版本默认babel-loader的配置中匹配文件规则不包括.mjs，因此需自行添加处理。
 */
export const MJS_PKG_LIST = ['js-base64'];

export const CHANNEL_PROJECT_CONFIG = {
  swan: { configFileSuffix: 'swan' },
  weapp: { configFileSuffix: 'config' },
  alipay: { configFileSuffix: '' },
  tt: { configFileSuffix: 'config' },
};
