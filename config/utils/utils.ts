import path from 'path';
import fs from 'fs';
import { parse } from '@babel/parser';

const pages :string[] = [];

const getAllPagesPath = () => {
  const configPath = path.join(__dirname, '../../', `project/${process.env.npm_config_dir}/src/app.config.ts`);
  const configJson = require(configPath)?.default;
  if(configJson?.pages || configJson?.subPackages) {
    configJson.pages?.forEach?.(pageItem => {
      pages.push(pageItem);
    })
    configJson.subPackages?.forEach?.(subPkgItem => {
      subPkgItem?.pages?.forEach?.(pageItem => {
        pages.push(`${subPkgItem.root}/${pageItem}`);
      })
    })
  }else {
    const configText = fs.readFileSync(configPath, 'utf-8');
    const ast = parse(configText, {
      sourceType: 'module',
    });
    ast.program.body.forEach((item:any) => {
      if (item.type === 'ExportDefaultDeclaration') {
        item.declaration.properties.forEach(childItem => {
          const { key = {} } = childItem;
          if (key.name === 'pages') {
            pages.push(...childItem.value.elements.map(pageItem => pageItem.value));
          } else if (key.name === 'subPackages') {
            childItem.value.elements.forEach(subPkgItem => {
              const pagesRoot = subPkgItem.properties.find(i => (i.key || {}).name === 'pages');
              const subPkgPaths = pagesRoot.value.elements.map(
                subPkgPropItem => `${subPkgItem.properties[0].value.value}/${subPkgPropItem.value}`
              );
              pages.push(...subPkgPaths);
            });
          }
        });
      }
    });
  }
}

const isPage = filePath => {
  if (!pages.length) {
    getAllPagesPath();
  }
  const relFilePath = filePath.replace(/\\/g, '/');
  const result = pages.some(item => relFilePath.indexOf(item) > 0);
  return result;
};

export {
  isPage,
};
