const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const Client = require('ssh2-sftp-client');
const chalk = require('chalk');

chalk.enabled = true;
chalk.level = 1;

let sftp;
let watcher;

const cdnDirBase = '/app/oper/static/websites/';
let cdnDir = global.cdnDir;
const ftpDir = process.env.npm_config_dir;

const uploadAll = async () => {
  const staticPath = path.resolve(process.cwd(), `./project/${ftpDir}/src/static`);
  if (!fs.existsSync(staticPath)) {
    console.log(chalk.red(`指定工程名 ${ftpDir} 不存在 static 文件夹！`));
    process.exit(0);
  }
  try {
    await connectFtp();
    sftp.on('upload', info => {
      console.log(chalk.yellow(`FTP: 文件 ${info.source} 上传成功！`));
    });
    let rslt = await sftp.uploadDir(staticPath, cdnDir);
    return rslt;
  } catch (e) {
    console.log('err', e);
  } finally {
    sftp.end();
  }
};

const winPath = tpath => {
  const isExtendedLengthPath = /^\\\\\?\\/.test(tpath);

  if (isExtendedLengthPath) {
    return path;
  }

  return tpath.replace(/\\/g, '/');
};

function uploadFile(file, fileName) {
  try {
    sftp
      .fastPut(file, `${cdnDirBase}${cdnDir}`)
      .then(() => {
        console.log(chalk.yellow(`FTP：${fileName} 上传开发环境成功!`));
      })
      .cache(() => {
        console.log(chalk.red(`FTP：${fileName} 上传开发环境失败!`));
      });
  } catch (error) {
    // console.log(chalk.red(`FTP：${fileName} 上传失败!`));
  }
}

function deleteFile(fileName) {
  try {
    sftp
      .delete(`${cdnDirBase}${cdnDir}`)
      .then(() => {
        console.log(chalk.yellow(`FTP：${fileName} 删除成功!`));
      })
      .cache(() => {
        console.log(chalk.red(`FTP：${fileName} 删除失败!`));
      });
  } catch (error) {
    // console.log(chalk.red(`FTP：${fileName} 删除失败!`));
  }
}

function createWatcher(wpath) {
  watcher = chokidar.watch(wpath, {
    // ignore .dotfiles and _mock.js
    ignored: /(^|[\/\\])(_mock.js$|\..)/,
    ignoreInitial: true,
  });
  // watcher.on(
  //   'all',
  //   async (event, tpath) => {
  //     const wtpath = winPath(tpath) as string;
  //     if (!wtpath.endsWith('/static')) {
  //       const pathArr = wtpath.split('/');
  //       const fileName = pathArr[pathArr.length - 1];
  //       if (event === 'unlink') {
  //         deleteFile(fileName);
  //       } else {
  //         uploadFile(wtpath, fileName);
  //       }
  //     }
  //   }
  // );
  watcher
    .on(
      'add',
      async (tpath) => {
        const wtpath = winPath(tpath);
        const pathArr = wtpath.split('/');
        const fileName = pathArr[pathArr.length - 1];
        uploadFile(wtpath, fileName);
      }
    )
    .on(
      'change',
      async (tpath) => {
        const wtpath = winPath(tpath);
        const pathArr = wtpath.split('/');
        const fileName = pathArr[pathArr.length - 1];
        uploadFile(wtpath, fileName);
      }
    )
    .on(
      'unlink',
      async (tpath) => {
        const wtpath = winPath(tpath);
        const pathArr = wtpath.split('/');
        const fileName = pathArr[pathArr.length - 1];
        deleteFile(fileName);
      }
    );
}

const connectFtp = async watchPath => {
  try {
    sftp = new Client();
    await sftp
      .connect({
        host: '**************', // ftp服务器ip地址
        port: '22', // ftp服务器port
        username: 'oper', // 你的登录用户名
        password: 'oper#gzhc2015', // 你的密码
        //  privateKey: fs.readFileSync('/Users/<USER>/.ssh/id_rsa'), // 私钥
        //  passphrase: 'yourpass', // 私钥密码
      });
    console.log(chalk.green(`FTP：连接成功!`));
    if (watchPath) {
      createWatcher(watchPath);
    }
  } catch (error) {
    console.log(chalk.red(`FTP：连接失败!`));
  }
};

if (!cdnDir) {
  if (!ftpDir) {
    console.log(chalk.red('请指定工程名，如：npm run ftp --dir=p099'));
    process.exit(0);
  }
  cdnDir = `${cdnDirBase}miniprogram-static/fe-his-mixapp/${ftpDir}`;
  uploadAll();
} else {
  cdnDir = `${cdnDirBase}${cdnDir}`;
}

module.exports = connectFtp;
