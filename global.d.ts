declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.scss';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';

declare namespace NodeJS {
  interface ProcessEnv {
    TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd';
  }
}

declare const ENV: string;
declare const API_PREFIX: string;
declare const PAY_PREFIX: string;
declare const IM_PREFIX: string;
declare const PRIMARY_COLOR: string;
declare const HIS_NAME: string;
declare const CDN_PREFIX: string;
declare const ENV_PREFIX: string;
declare const NPM_SCRIPT_EVENT: string;
declare const SWAN_BDJK_ENV: string;

declare const my: any;
declare const wx: any;

declare type IHCAPIRESPONSE<T> = {
  code: number;
  msg?: string;
  data: T;
};

declare type IHCPAGEDATA<T> = {
  beginIndex: number;
  beginPageIndex: number;
  currentPage: number;
  endPageIndex: number;
  numPerPage: number;
  pageCount: number;
  recordList: T[];
  totalCount: number;
  list: T[];
};

declare type IHCDICTIONARIEITEM = {
  dictKey: string;
  dictValue: string;
};

declare type IHCDICTIONARIES = IHCDICTIONARIEITEM[];

declare type IHCPatCard = {
  bindMedicareCard: number;
  bindStatus: number;
  healthCardFlag: number;
  idNo: string;
  idType: number;
  idTypeName: string;
  isDefault: number;
  patCardNo: string;
  patCardNoEncry: string;
  patCardType: number;
  patCardTypeName: string;
  patHisNo: string;
  patInNo: string;
  patientFullIdNo: string;
  patientId: string;
  patientImg: string;
  patientMobile: string;
  patientName: string;
  patientSex: string;
  relationName: string;
  relationType: number;
};
