{"name": "fe-his-mixapp-dev", "version": "1.0.0", "description": "融合版父工程", "templateInfo": {"name": "default", "typescript": true, "css": "scss"}, "scripts": {"build:weapp": "cross-env RUN_ENV='prod' ken build --type weapp", "bd:weapp": "cross-env RUN_ENV='prod' ken build --type weapp", "bd:alipay": "cross-env RUN_ENV='prod' ken build --type alipay", "build:swan": "cross-env RUN_ENV='prod' ken build --type swan", "build:alipay": "cross-env RUN_ENV='prod' ken build --type alipay", "build:tt": "cross-env RUN_ENV='prod' ken build --type tt", "build:h5": "cross-env RUN_ENV='prod' ken build --type h5", "build:rn": "cross-env RUN_ENV='prod' ken build --type rn", "build:qq": "cross-env RUN_ENV='prod' ken build --type qq", "build:jd": "cross-env RUN_ENV='prod' ken build --type jd", "build:quickapp": "cross-env RUN_ENV='prod' ken build --type quickapp", "dev:weapp": "cross-env RUN_ENV='dev' ken build --type weapp --watch", "dev:swan": "cross-env RUN_ENV='dev' ken build --type swan --watch", "dev:alipay": "cross-env RUN_ENV='dev' ken build --type alipay --watch", "dev:tt": "cross-env RUN_ENV='dev' ken build --type tt --watch", "dev:h5": "cross-env RUN_ENV='dev' ken build --type h5 --watch", "dev:rn": "cross-env RUN_ENV='dev' ken build --type rn --watch", "dev:qq": "cross-env RUN_ENV='dev' ken build --type qq --watch", "dev:jd": "cross-env RUN_ENV='dev' ken build --type jd --watch", "dev:quickapp": "cross-env RUN_ENV='dev' ken build --type quickapp --watch", "uat:weapp": "cross-env RUN_ENV='uat' ken build --type weapp", "uat:alipay": "cross-env RUN_ENV='uat' ken build --type alipay", "uuat:weapp": "cross-env RUN_ENV='uuat' ken build --type weapp", "uuat:alipay": "cross-env RUN_ENV='uuat' ken build --type alipay", "stylelint": "stylelint --fix \"./src/**/*.{css,less,html}\"", "eslint": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "prettier": "prettier --write \"./src/**/*.{html,json}\"", "ftp": "node ftp"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "7.14.0", "@haici/gmsm4": "^0.0.4", "@haici/his-miniapp-monitor-sdk": "^1.0.4", "@haici/im-wx": "1.0.3-beta.5", "@haici/less-variable": "^1.0.2", "@haici/taro-debug": "^0.0.5", "@haici/taro-inject-loader": "^1.0.2", "@haici/taro-request": "2.1.3", "@haici/taro-request-filter": "2.1.8", "@haici/utils": "^1.1.2", "@tarojs/components": "3.4.12", "@tarojs/react": "3.4.12", "@tarojs/runtime": "3.4.12", "@tarojs/taro": "3.4.12", "@types/react-redux": "^7.1.11", "async-validator": "^3.5.1", "base-64": "^1.0.0", "blueimp-md5": "^2.18.0", "classnames": "^2.2.6", "dayjs": "^1.9.7", "dva-core": "^2.0.3", "dva-loading": "^3.0.21", "echarts-taro3-react": "^1.0.8", "lodash.memoize": "^4.1.2", "lodash.merge": "^4.6.2", "mini-html-parser2": "^0.3.0", "rc-util": "5.21.5", "react": "^16.10.0", "react-dom": "^16.10.0", "react-redux": "^7.2.2", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "ssh2-sftp-client": "^5.3.1", "taro-code": "^3.3.0", "taro-ui": "3.1.0-beta.2", "taro3-code": "^2.2.0", "trtc-wx-sdk": "^1.1.4", "vod-wx-sdk-v2": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@haici/ken-cli": "^0.1.0", "@tarojs/cli": "3.4.12", "@tarojs/mini-runner": "3.4.12", "@tarojs/plugin-framework-react": "3.4.12", "@tarojs/webpack-runner": "3.4.12", "@types/node": "^14.14.20", "@types/react": "^16.10.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^2.x", "@typescript-eslint/parser": "^2.x", "airbnb": "^0.0.2", "babel-plugin-import": "^1.13.3", "babel-plugin-lodash": "^3.3.4", "babel-preset-taro": "3.4.12", "camel-2-dash": "^0.1.0", "cross-env": "^7.0.2", "eslint": "^7.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-taro": "3.4.12", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "4.2.0", "eslint-plugin-unicorn": "^23.0.0", "glob": "^7.1.6", "husky": "^4.3.0", "lint-staged": "^10.5.1", "lodash-webpack-plugin": "^0.11.5", "prettier": "^2.2.0", "stylelint": "13.8.0", "stylelint-config-prettier": "^8.0.2", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.1.0", "stylelint-prettier": "^1.1.2", "taro-iconfont-cli": "^3.1.1", "taro-inject-component-loader": "^2.1.0", "typescript": "^3.7.0"}, "resolutions": {"@babel/runtime": "7.7.7", "@types/react": "17.0.2"}}