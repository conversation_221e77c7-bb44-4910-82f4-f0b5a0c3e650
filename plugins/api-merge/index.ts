import fs from 'fs';
import path from 'path';
import chokidar from 'chokidar';
import glob from 'glob';
import { EOL } from 'os';
import lodash from 'lodash';

const winPath = (tpath: string) => {
  const isExtendedLengthPath = /^\\\\\?\\/.test(tpath);

  if (isExtendedLengthPath) {
    return path;
  }

  return tpath.replace(/\\/g, '/');
};

const genCodes = (imports: string[]) => {
  const importItems: string[] = imports.map(
    (ele, index) =>
      `import { Request as Q${index}, Response as P${index} } from '${ele.replace('.d.ts', '')}';`
  );
  const exportItems: string[][] = imports.reduce(
    (pre, _, key) => {
      pre[0].push(`Q${key}`);
      pre[1].push(`P${key}`);
      return pre;
    },
    [[], []] as any
  );
  const codes: any = [
    ...importItems,
    `
type IRequestApis = ${exportItems[0].join(' & ')}
type IResponseApis = ${exportItems[1].join(' & ')}

export type IApiKeys = keyof IResponseApis & keyof IRequestApis;
export type IRequest<T extends IApiKeys> = IRequestApis[T];
export type IResponse<T extends IApiKeys> = IResponseApis[T];
`,
  ].join(EOL);
  return codes;
};

function createWatcher(wpath: string) {
  const watcher = chokidar.watch(`${wpath}/**/api.d.ts`, {
    // ignore .dotfiles and _mock.js
    ignored: /(^|[\/\\])(_mock.js$|\..)/,
    ignoreInitial: false,
  });
  watcher.on(
    'all',
    lodash.debounce(async (event, tpath) => {
      // debug(`${event} ${path}`);
      // await generate();
      if (['unlink', 'add'].includes(event)) {
        reWriteFile(wpath);
      }
    }, 500)
  );
}

function reWriteFile(sourcePath) {
  const files = glob.sync('**/api.d.ts', {
    cwd: sourcePath,
  });
  const filterPath = lodash.uniq(files);
  const absPath = filterPath.map(i => {
    // const abs = path.resolve(__dirname, sourcePath, i)
    // return winPath(abs)
    return `@@/${i}`;
  });
  const codes = genCodes(absPath);
  const apiFilePath = path.resolve(__dirname, `${sourcePath}/.tmp/MAPI.d.ts`);
  fs.writeFileSync(apiFilePath, codes, 'UTF-8');
}

export default (ctx: any) => {
  ctx.onBuildStart(() => {
    const tmpPath = path.resolve(ctx.paths.sourcePath, './.tmp');
    if (!fs.existsSync(tmpPath)) {
      fs.mkdirSync(tmpPath);
    }
    reWriteFile(ctx.paths.sourcePath);
    if (ctx.runOpts.isWatch) {
      createWatcher(ctx.paths.sourcePath);
    }
  });
};
