const isTrueImport = (name, path, nodePath) => {
  return (
    nodePath.get('source').isStringLiteral() &&
    nodePath.get('source').node.value === path &&
    nodePath.get('specifiers')[0].node.local.name === name
  );
};

const isTrueRequire = (name, path, nodePath) => {
  const declaration = nodePath.get('declarations')[0];
  const id = declaration.get('id');
  const init = declaration.get('init');
  return (
    init.isCallExpression() &&
    init.node.callee.name === 'require' &&
    init.node.arguments.some(item => item.value === path) &&
    id.node.name === name
  );
};

module.exports = function ({ types: t }) {
  return {
    visitor: {
      JSXOpeningElement(path, { opts }) {
        const { components = [] } = opts;

        if (!components || components.length < 1) {
          // 无 components 配置
          return;
        }

        const componentOpts = components.find(c => c.name === path.get('name').node.name);
        if (!componentOpts) {
          // 非自动引入组件
          return;
        }

        const bodyPath = path.findParent(p => p.isProgram()).get('body');
        const hasRequireOrImport = bodyPath.some(nodePath => {
          // 判断是否已经 import
          if (nodePath.isImportDeclaration()) {
            return isTrueImport(componentOpts.name, componentOpts.path, nodePath);
          }
          // 判断是否已经 require
          if (nodePath.isVariableDeclaration()) {
            return isTrueRequire(componentOpts.name, componentOpts.path, nodePath);
          }
        });

        if (!hasRequireOrImport) {
          // 插入 import 组件代码
          const importDefaultSpecifier = [
            t.ImportDefaultSpecifier(t.Identifier(componentOpts.name)),
          ];
          const importDeclaration = t.ImportDeclaration(
            importDefaultSpecifier,
            t.StringLiteral(componentOpts.path)
          );
          bodyPath[0].insertBefore(importDeclaration);
        }
      },
    },
  };
};
