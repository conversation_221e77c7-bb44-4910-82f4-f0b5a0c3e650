## 小程序配置

1. 在 [支付宝小程序后台](https://open.alipay.com/platform/developerIndex.htm) 选择区域后台同事为医院配置好的开发小程序账号。

2. 进入 开发服务 - 版本管理：点击 添加能力 按钮，至少添加能力项[小程序支付、获取会员基础信息、获取会员手机号、模板消息]。

3. 进入 开发服务 - 开发设置 - 开发设置，添加 服务器域名白名单，添加域名 smix.med.gzhc365.com，umix.med.gzhc365.com，mix.med.gzhc365.com，spay.med.gzhc365.com，pay.med.gzhc365.com，upay.med.gzhc365.com，openmonitor.alipay-eco.com，ifex.med.gzhc365.com。

4. 进入 小程序管理 - 成员管理，将自己的支付宝账号添加为开发者，并且在手机端支付宝通过开发者添加邀请。

5. 下载 [支付宝开发者工具](https://render.alipay.com/p/f/fd-jwq8nu2a/pages/home/<USER>

## 接入步骤

1. 找成某在 [项目组](http://**************/fe-his-mixapp) 下建立实施医院项目。

2. clone 代码至 [脚手架](http://**************/fe-his-mixapp/fe-his-mixapp-dev) 的 project 文件架下。

3. 修改 p099/config/index.ts 文件的 HIS_NAME 为对应医院名称，修改 p099/src/config/constant.ts 下的 BASE_KEY(后端提供)、HIS_INFO、HIS_SCOPE 字段为对应医院的信息。【_注意：HIS_SCOPE 中标注了开通 E_HIS(互联网医院),W_HIS(智慧医院) 字段作为是否开通相关功能的开关，请根据医院实际情况配置，默认开启智慧医院，关闭互联内网医院_】

4. 运行编译命令（详见 README.md）(_Tip：可首先运行 npm run ftp --dir=pxxx 将静态资源上传至开发环境 ftp 服务器_)。

5. 打开开发者工具，选择右上角的 打开项目，选择编译后的 dist 包(如 p099 路径：./dev 脚手架/dist/p099/alipay/)。

![如图](https://static.med.gzhc365.com/fss/publicfile/7be20e18dc64c72b52a0ef6f796375c9f107ccaa6158c645b2ab6ce2a4021f01.png '效果图')

6. 选择配置好的小程序作为开发小程序，

![示例图](https://static.med.gzhc365.com/fss/publicfile/1bc812b289b67ad475d74661da5af870aa57f46a82596a5443b1b228a0febef8.png)

7. 修改项目 app.tsx 支付宝数据埋点的 appid(找运营要，每加医院不同)
   ![](https://cspub.med.gzhc365.com/fss/publicfile/f1baca63400c603e3c39c3cffddda5f021d1769867ad40dba4b790e06519379a.png)

## 运营后台配置

1. 登录 https://ssso.med.gzhc365.com (注：无权限可找肖琼申请)，选择 运营后台开发稳定版。

2. 提示语配置

![](http://**************/pages/viewpage.action?pageId=76808247)

3. 小程序首页配置

![](https://static.med.gzhc365.com/fss/publicfile/19a9a964863eb446f6685c5d03731bed579ef5bd80c6ff41d5dee6078022e5dd.png)

4. 如若步骤 3 中选择渠道处无 支付宝小程序 渠道，可参照下图算便为支付宝小程序添加一项功能，然后返回 小程序首页配置。

![](https://static.med.gzhc365.com/fss/publicfile/cb427b0c334d4514d41a779593a01a35fe377a00e72161b55cf8eaa4cd2b9f00.png)

5. 若医院功能路径与标准功能有差异，可以不使用默认功能，而是选择功能集合的 添加自定义功能。

6. 接入支付宝光华平台
   登陆账号：<EMAIL>
   登陆密码：gzhcxxhh2017
   手机验证码：程翠华手机号码

登陆：https://openmonitor.alipay.com
