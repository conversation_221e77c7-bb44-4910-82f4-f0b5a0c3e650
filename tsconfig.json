{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "baseUrl": ".", "rootDir": ".", "jsx": "react", "jsxFactory": "React.createElement", "allowJs": true, "resolveJsonModule": true, "jsxFragmentFactory": "React.Fragment", "typeRoots": ["node_modules/@types", "global.d.ts"], "paths": {"@@/*": ["./src/*"], "@com/*": ["./src/components/*"], "@config/*": ["./src/config/*"], "@res/*": ["./src/resource/*"], "@utils/*": ["./src/utils/*"]}}, "exclude": ["node_modules", "dist"], "compileOnSave": false}